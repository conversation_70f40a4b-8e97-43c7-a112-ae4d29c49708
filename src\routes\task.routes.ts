import { Router } from 'express';
import {
  createTask,
  getAllTasks,
  getTaskById,
  updateTask,
  deleteTask
} from '../controllers/task.controller';
import { authenticateToken } from '../middlewares/index';

const router = Router();

// Apply JWT authentication middleware to all task routes
router.use(authenticateToken);
router.post('/', createTask);
router.get('/', getAllTasks);
router.get('/:taskId', getTaskById);
router.put('/:taskId', updateTask);
router.delete('/:taskId', deleteTask);

export default router;

