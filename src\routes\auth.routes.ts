import { Router } from 'express';
import { validateSignupRequest } from '../middlewares/index';
import 
{
    signup,
    login,
    resetPasswordGenerateOTP,
    verifyOtp,
    resetPassword,
} from '../controllers/auth.controller';

const router = Router();

router.post('/signup', validateSignupRequest, signup);
router.post('/login', login);
router.post('/forgot-password', resetPasswordGenerateOTP);
router.post('/resend-otp', resetPasswordGenerateOTP);
router.post('/verify-otp', verifyOtp);
router.post('/reset-password', resetPassword);   

export default router;
