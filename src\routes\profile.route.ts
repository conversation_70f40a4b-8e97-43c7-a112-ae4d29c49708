import { Router } from 'express';
import { createProfile, updateProfile, getProfile, changePassword } from '../controllers/profile.controller';
import { authenticateToken } from '../middlewares/index';

const router = Router();

router.use(authenticateToken);
router.post('/create-profile', createProfile);
router.patch('/update-profile', updateProfile);
router.get('/get-profile', getProfile);
router.put('/change-password', changePassword);

export default router;