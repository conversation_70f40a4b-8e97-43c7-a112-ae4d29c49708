import dotenv from 'dotenv';
import express from 'express';
import connectDB from './config/db';
import cors from "cors";
import bodyParser from "body-parser";
import helmet from "helmet";
import { rateLimiter } from "./utils/rateLimit";
import { StatusCodes } from "http-status-codes";
import {
  authRouter,
  profileRouter,
  taskRouter,
} from "./routes/index";

// ----------- Config -------------------
dotenv.config();

// ----------- Server -------------------
const app = express();
const port = process.env.PORT || 5001;

// ---------- Middlewares ----------------------------
app.use(bodyParser.json({ limit: "50mb" }));
app.use(bodyParser.urlencoded({ limit: "50mb", extended: true }));
app.use(cors());
app.use(helmet());
app.use(rateLimiter()); 


// ----------- Routes -------------------
app.use('/api/auth', authRouter);
app.use('/api/profile', profileRouter);
app.use('/api/tasks', taskRouter);        //home

// Connect to MongoDB
connectDB();

app.listen(5001, () => {
  console.log('Server is running on port 5001');
});
