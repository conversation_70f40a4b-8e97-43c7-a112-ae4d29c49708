import mongoose, { Schema } from 'mongoose';
import { ITask, IToDoItem } from '../interfaces/tasks';

const toDoItemSchema: Schema = new Schema({
  toDoTask: { type: String, required: true },
  isCompleted: { type: Boolean, default: false }
}, { _id: false });

const taskSchema: Schema = new Schema({
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  title: { 
    type: String, 
    required: true,
    trim: true
  },
  category: [{ 
    type: String, 
    required: true,
    trim: true
  }],
  date: { 
    type: Date, 
    required: true 
  },
  startTime: { 
    type: String, 
    required: true,
    trim: true
  },
  endTime: { 
    type: String, 
    required: true,
    trim: true
  },
  description: { 
    type: String, 
    required: true,
    trim: true
  },
  toDoList: [toDoItemSchema],
  reminder: { 
    type: String, 
    required: true,
    trim: true
  },
  taskCompletionPercentage: { 
    type: Number, 
    default: 0 
  }
}, {
  timestamps: true
});

// Index for better query performance
taskSchema.index({ userId: 1, date: 1 });
taskSchema.index({ userId: 1, category: 1 });

const Task = mongoose.model<ITask>('Task', taskSchema);
export default Task;
