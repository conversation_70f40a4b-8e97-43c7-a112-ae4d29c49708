import mongoose, { Schema, Document } from 'mongoose';
import { IProfile } from '../interfaces/index';

const profileSchema: Schema = new Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  username: { type: String, required: true },
  gender: { type: String, required: true },
  dob: { type: Date, required: true },
  about: { type: String, required: true },
});

const Profile = mongoose.model<IProfile>('Profile', profileSchema);
export default Profile;
