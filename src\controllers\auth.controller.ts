import type { Request, Response } from 'express';
import dotenv from 'dotenv';
import bcrypt from 'bcrypt';
import User from '../models/User';
import jwt from 'jsonwebtoken';
import { StatusCodes } from 'http-status-codes';
import nodemailer from 'nodemailer';

dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET;
if (!JWT_SECRET) {
  throw new Error('JWT_SECRET is not defined in the environment variables');
}

export const signup = async (req: Request, res: Response) => {
  const { email, password, confirmPassword } = req.body;

  try {
    if (password !== confirmPassword) {
    console.log(password, confirmPassword);
    return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Passwords do not match' });
  }
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'User already exists' });
    }
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create the new user
    const newUser = new User({
      email,
      password: hashedPassword
    });

    await newUser.save();

    // Create JWT token
    const payload = { userId: newUser._id, email: newUser.email }
    const accessToken = jwt.sign(payload, JWT_SECRET, 
      { expiresIn: '30d' }); 
    return res.status(StatusCodes.OK).json({
      message: "Signed Up Successfully!",
      user: newUser,   // cahnge here to deselct the pass in res
      accessToken,
    });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Error signing up', error }); // 
  }
};



// Login function
export const login = async (req: Request, res: Response) => {
  const { email, password } = req.body;
//user id = beatrer token
  try {
    // Check if user exists
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(StatusCodes.UNAUTHORIZED).json({ message: 'Invalid email or account not registered' });
    }

    // Verify the password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(StatusCodes.UNAUTHORIZED).json({ message: 'Invalid password' });
    }

    // Generate JWT token
    const payload = { userId: user._id, email: user.email };
    const accessToken = jwt.sign(payload, JWT_SECRET, { expiresIn: '30d' });

    return res.status(StatusCodes.OK).json({
      message: 'User Logged In Successfully',
      user: { email: user.email, userId: user._id }, 
      accessToken,
    });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Error logging in', error });
  }
};


// Helper function to send OTP email
const sendOtpEmail = async (email: string, otp: string) => {
  const transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
      user: process.env.EMAIL_USER, 
      pass: process.env.EMAIL_PASS
    }
  });

  const mailOptions = {
    from: process.env.EMAIL_USER,
    to: email,
    subject: 'Password Reset OTP',
    text: `Your OTP for password reset is: ${otp}`
  };

  await transporter.sendMail(mailOptions);
};

// Generate OTP and store it in the user's document (when user click on reset password it will generate OTP and send it to user's email)
export const resetPasswordGenerateOTP = async (req: Request, res: Response) => {
  const { email } = req.body;

  try {
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Email not found' });
    }

    // Generate 4-digit OTP
    const otp = Math.floor(1000 + Math.random() * 9000).toString();

    // Send OTP to user's email
    await sendOtpEmail(email, otp);

    // Set OTP value to null before storing it (ensures old value is cleared)
    user['otp'] = "";  
    user['otpCreatedAt'] = new Date();   // Store the current time as OTP generation time
    user['otp'] = otp;   // Store the new OTP
    await user.save();

    return res.status(StatusCodes.OK).json({ message: 'OTP sent to your email' });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Error sending OTP', error });
  }
};


// Verify OTP function
export const verifyOtp = async (req: Request, res: Response) => {
  const { email, otp } = req.body;

  try {
    const user = await User.findOne({ email: req.body.email });
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Email not found' });
    }

    if (!user['otp']) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'OTP has not been generated. Click Resend to generate a new one.' });
    }

    // Check if OTP is expired ... 2 minutes expiration time
    console.log("OTP Created At:", user['otpCreatedAt']);
    const otpCreatedAt = user['otpCreatedAt'] instanceof Date ? user['otpCreatedAt'].getTime() : 0;
    const otpAge = new Date().getTime() - otpCreatedAt;
    console.log("OTP Age in milliseconds:", otpAge); 
    const otpExpirationTime = 2 * 60 * 1000; // 2 minutes in milliseconds
    if (otpAge > otpExpirationTime) {
      user['otp'] = "";  
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'OTP has expired. Please request a new OTP' });
    }

    if (user['otp'] !== otp) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'OTP does not match' });
    }

    user['otp'] = "";
    await user.save();

    return res.status(StatusCodes.OK).json({ message: 'OTP verified' });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Error verifying OTP', error });
  }
};


// Reset password function after OTP verification
export const resetPassword = async (req: Request, res: Response) => {
  const { email, password, confirmPassword } = req.body;

  try {

    // Ensure new password is not the same as the old one
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Email not found' });
    }

    const isSamePassword = await bcrypt.compare(password, user.password);
    if (isSamePassword) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'New password cannot be same as old, try a new password' });
    }

    // Ensure password and confirmPassword match
    if (password !== confirmPassword) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Passwords do not match' });
    }
    const hashedPassword = await bcrypt.hash(password, 10);
    user.password = hashedPassword;
    await user.save();

    return res.status(StatusCodes.OK).json({
      message: 'Password reset successfully. Please return to the login page.',
    });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Error resetting password', error });
  }
};